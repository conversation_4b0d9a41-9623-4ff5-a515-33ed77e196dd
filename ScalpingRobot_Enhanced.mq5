//+------------------------------------------------------------------+
//| ScalpingRobot_Enhanced.mq5                                      |
//| Enhanced version with improved accuracy and trade frequency      |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Enhanced Trading Systems"
#property link      "https://www.mql5.com"
#property version   "2.00"
#property description "Enhanced Scalping Robot with Multi-Indicator Confirmation"

#include <Trade/Trade.mqh>
#include <Indicators/Indicators.mqh>

CTrade         trade;
CPositionInfo  pos;
COrderInfo     ord;

// Indicator handles
int handleRSI, handleMACD, handleBB, handleATR, handleEMA_Fast, handleEMA_Slow, handleStoch;
int handleMA_HTF; // Higher timeframe MA

//+------------------------------------------------------------------+
//| Input Parameters                                                 |
//+------------------------------------------------------------------+
input group "=== BASIC TRADING SETTINGS ==="
input double RiskPercent = 1.5;                    // Risk as % of account balance
input double MaxRiskPerTrade = 3.0;                // Maximum risk per single trade
input int MaxPositions = 3;                        // Maximum positions per direction
input ENUM_TIMEFRAMES Timeframe = PERIOD_CURRENT;  // Trading timeframe
input ENUM_TIMEFRAMES HTF_Timeframe = PERIOD_H1;   // Higher timeframe for trend
input int inpMagic = 298347;                       // EA Magic Number
input string TradeComment = "Enhanced Scalping Robot";

input group "=== DYNAMIC TP/SL SETTINGS ==="
input bool UseDynamicTPSL = true;                  // Use ATR-based TP/SL
input double ATR_TP_Multiplier = 2.5;              // ATR multiplier for TP
input double ATR_SL_Multiplier = 1.5;              // ATR multiplier for SL
input int TpPoints = 200;                          // Fixed TP (if not using ATR)
input int Slpoints = 150;                          // Fixed SL (if not using ATR)
input double MinRiskReward = 1.5;                  // Minimum Risk:Reward ratio

input group "=== TRAILING STOP SETTINGS ==="
input bool UseTrailingStop = true;                // Enable trailing stop
input double TslTriggerATR = 1.0;                  // ATR multiplier to trigger TSL
input double TslATR = 0.5;                         // ATR multiplier for TSL distance
input int TslTriggerPoints = 15;                   // Fixed trigger points (backup)
input int TslPoints = 10;                          // Fixed TSL points (backup)

input group "=== SIGNAL CONFIRMATION SETTINGS ==="
input bool UseRSIFilter = true;                   // Use RSI for signal confirmation
input int RSI_Period = 14;                        // RSI period
input double RSI_Oversold = 30;                   // RSI oversold level
input double RSI_Overbought = 70;                 // RSI overbought level

input bool UseMACDFilter = true;                  // Use MACD for signal confirmation
input int MACD_Fast = 12;                         // MACD fast EMA
input int MACD_Slow = 26;                         // MACD slow EMA
input int MACD_Signal = 9;                        // MACD signal line

input bool UseBollingerFilter = true;             // Use Bollinger Bands
input int BB_Period = 20;                         // BB period
input double BB_Deviation = 2.0;                  // BB standard deviation

input bool UseStochasticFilter = true;            // Use Stochastic
input int Stoch_K = 5;                            // Stochastic %K
input int Stoch_D = 3;                            // Stochastic %D
input int Stoch_Slowing = 3;                      // Stochastic slowing

input group "=== TREND FILTER SETTINGS ==="
input bool UseTrendFilter = true;                 // Use trend filter
input int EMA_Fast_Period = 21;                   // Fast EMA period
input int EMA_Slow_Period = 50;                   // Slow EMA period
input int HTF_MA_Period = 200;                    // Higher timeframe MA period

input group "=== VOLATILITY & MARKET FILTERS ==="
input int ATR_Period = 14;                        // ATR period
input double MinVolatility = 0.0001;              // Minimum ATR for trading
input double MaxVolatility = 0.01;                // Maximum ATR for trading
input bool UseSpreadFilter = true;                // Use spread filter
input double MaxSpreadPips = 3.0;                 // Maximum spread in pips

input group "=== BREAKOUT DETECTION ==="
input int SwingBars = 5;                          // Bars for swing detection
input int LookbackBars = 100;                     // Bars to look back for swings
input double MinBreakoutStrength = 1.2;           // Minimum breakout strength (ATR multiplier)
input int ExpirationBars = 50;                    // Order expiration in bars
input double OrderDistanceATR = 0.5;               // Order distance from price (ATR multiplier)

input group "=== TIME FILTER ==="
enum StartHour {
    SH_Inactive = 0, SH_0100 = 1, SH_0200 = 2, SH_0300 = 3, SH_0400 = 4, SH_0500 = 5,
    SH_0600 = 6, SH_0700 = 7, SH_0800 = 8, SH_0900 = 9, SH_1000 = 10, SH_1100 = 11,
    SH_1200 = 12, SH_1300 = 13, SH_1400 = 14, SH_1500 = 15, SH_1600 = 16, SH_1700 = 17,
    SH_1800 = 18, SH_1900 = 19, SH_2000 = 20, SH_2100 = 21, SH_2200 = 22, SH_2300 = 23
};

enum EndHour {
    EH_Inactive = 0, EH_0100 = 1, EH_0200 = 2, EH_0300 = 3, EH_0400 = 4, EH_0500 = 5,
    EH_0600 = 6, EH_0700 = 7, EH_0800 = 8, EH_0900 = 9, EH_1000 = 10, EH_1100 = 11,
    EH_1200 = 12, EH_1300 = 13, EH_1400 = 14, EH_1500 = 15, EH_1600 = 16, EH_1700 = 17,
    EH_1800 = 18, EH_1900 = 19, EH_2000 = 20, EH_2100 = 21, EH_2200 = 22, EH_2300 = 23
};

input StartHour SHInput = SH_0700;                // Trading start hour
input EndHour EHInput = EH_2100;                  // Trading end hour
input bool AvoidNews = true;                      // Avoid trading during news
input int NewsAvoidMinutes = 30;                  // Minutes to avoid before/after news

//+------------------------------------------------------------------+
//| Global Variables                                                 |
//+------------------------------------------------------------------+
datetime lastBarTime = 0;
double currentATR = 0;
int totalBuyPositions = 0;
int totalSellPositions = 0;
double accountEquity = 0;
double maxDrawdown = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    // Set magic number
    trade.SetExpertMagicNumber(inpMagic);
    
    // Initialize indicators
    if(!InitializeIndicators()) {
        Print("Failed to initialize indicators");
        return INIT_FAILED;
    }
    
    Print("Enhanced Scalping Robot initialized successfully");
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Release indicator handles
    ReleaseIndicators();
    Print("Enhanced Scalping Robot deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Update account info
    UpdateAccountInfo();
    
    // Handle trailing stops on every tick
    if(UseTrailingStop) {
        TrailStop();
    }
    
    // Check for new bar
    if(!IsNewBar()) return;
    
    // Update current ATR
    UpdateATR();
    
    // Check trading conditions
    if(!IsTradingAllowed()) {
        CloseAllOrders();
        return;
    }
    
    // Count current positions
    CountPositions();
    
    // Look for trading opportunities
    CheckTradingSignals();

    // Print statistics periodically
    PrintStatistics();
}

//+------------------------------------------------------------------+
//| Initialize all indicators                                        |
//+------------------------------------------------------------------+
bool InitializeIndicators() {
    // RSI
    if(UseRSIFilter) {
        handleRSI = iRSI(_Symbol, Timeframe, RSI_Period, PRICE_CLOSE);
        if(handleRSI == INVALID_HANDLE) return false;
    }
    
    // MACD
    if(UseMACDFilter) {
        handleMACD = iMACD(_Symbol, Timeframe, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
        if(handleMACD == INVALID_HANDLE) return false;
    }
    
    // Bollinger Bands
    if(UseBollingerFilter) {
        handleBB = iBands(_Symbol, Timeframe, BB_Period, 0, BB_Deviation, PRICE_CLOSE);
        if(handleBB == INVALID_HANDLE) return false;
    }
    
    // Stochastic
    if(UseStochasticFilter) {
        handleStoch = iStochastic(_Symbol, Timeframe, Stoch_K, Stoch_D, Stoch_Slowing, MODE_SMA, STO_LOWHIGH);
        if(handleStoch == INVALID_HANDLE) return false;
    }
    
    // ATR
    handleATR = iATR(_Symbol, Timeframe, ATR_Period);
    if(handleATR == INVALID_HANDLE) return false;
    
    // EMAs for trend
    if(UseTrendFilter) {
        handleEMA_Fast = iMA(_Symbol, Timeframe, EMA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
        handleEMA_Slow = iMA(_Symbol, Timeframe, EMA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);
        handleMA_HTF = iMA(_Symbol, HTF_Timeframe, HTF_MA_Period, 0, MODE_EMA, PRICE_CLOSE);
        
        if(handleEMA_Fast == INVALID_HANDLE || handleEMA_Slow == INVALID_HANDLE || handleMA_HTF == INVALID_HANDLE)
            return false;
    }
    
    return true;
}

//+------------------------------------------------------------------+
//| Release indicator handles                                        |
//+------------------------------------------------------------------+
void ReleaseIndicators() {
    if(handleRSI != INVALID_HANDLE) IndicatorRelease(handleRSI);
    if(handleMACD != INVALID_HANDLE) IndicatorRelease(handleMACD);
    if(handleBB != INVALID_HANDLE) IndicatorRelease(handleBB);
    if(handleATR != INVALID_HANDLE) IndicatorRelease(handleATR);
    if(handleEMA_Fast != INVALID_HANDLE) IndicatorRelease(handleEMA_Fast);
    if(handleEMA_Slow != INVALID_HANDLE) IndicatorRelease(handleEMA_Slow);
    if(handleStoch != INVALID_HANDLE) IndicatorRelease(handleStoch);
    if(handleMA_HTF != INVALID_HANDLE) IndicatorRelease(handleMA_HTF);
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar() {
    datetime currentBarTime = iTime(_Symbol, Timeframe, 0);
    if(lastBarTime != currentBarTime) {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Update current ATR value                                         |
//+------------------------------------------------------------------+
void UpdateATR() {
    double atr[];
    if(CopyBuffer(handleATR, 0, 1, 1, atr) > 0) {
        currentATR = atr[0];
    }
}

//+------------------------------------------------------------------+
//| Update account information                                       |
//+------------------------------------------------------------------+
void UpdateAccountInfo() {
    accountEquity = AccountInfoDouble(ACCOUNT_EQUITY);
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double currentDrawdown = (balance - accountEquity) / balance * 100;
    if(currentDrawdown > maxDrawdown) {
        maxDrawdown = currentDrawdown;
    }
}

//+------------------------------------------------------------------+
//| Check if trading is allowed                                      |
//+------------------------------------------------------------------+
bool IsTradingAllowed() {
    // Check time filter
    if(!IsWithinTradingHours()) return false;

    // Check spread filter
    if(UseSpreadFilter && !IsSpreadAcceptable()) return false;

    // Check volatility filter
    if(!IsVolatilityAcceptable()) return false;

    // Check news filter
    if(AvoidNews && IsNewsTime()) return false;

    // Check maximum drawdown
    if(maxDrawdown > 20.0) return false; // Stop trading if drawdown > 20%

    return true;
}

//+------------------------------------------------------------------+
//| Check if within trading hours                                    |
//+------------------------------------------------------------------+
bool IsWithinTradingHours() {
    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);
    int currentHour = time.hour;

    int startHour = (int)SHInput;
    int endHour = (int)EHInput;

    if(startHour == 0 && endHour == 0) return true; // No time restriction

    if(startHour <= endHour) {
        return (currentHour >= startHour && currentHour < endHour);
    } else {
        return (currentHour >= startHour || currentHour < endHour);
    }
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable                                    |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable() {
    double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
    double spreadPips = spread / (10 * _Point);
    return (spreadPips <= MaxSpreadPips);
}

//+------------------------------------------------------------------+
//| Check if volatility is acceptable                               |
//+------------------------------------------------------------------+
bool IsVolatilityAcceptable() {
    return (currentATR >= MinVolatility && currentATR <= MaxVolatility);
}

//+------------------------------------------------------------------+
//| Check if it's news time (simplified implementation)             |
//+------------------------------------------------------------------+
bool IsNewsTime() {
    // This is a simplified implementation
    // In a real EA, you would integrate with a news calendar API
    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);

    // Avoid trading during typical news hours (example: 8:30, 10:00, 14:00, 16:00 GMT)
    int minute = time.hour * 60 + time.min;
    int newsMinutes[] = {510, 600, 840, 960}; // 8:30, 10:00, 14:00, 16:00 in minutes

    for(int i = 0; i < ArraySize(newsMinutes); i++) {
        if(MathAbs(minute - newsMinutes[i]) <= NewsAvoidMinutes) {
            return true;
        }
    }
    return false;
}

//+------------------------------------------------------------------+
//| Count current positions                                          |
//+------------------------------------------------------------------+
void CountPositions() {
    totalBuyPositions = 0;
    totalSellPositions = 0;

    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == inpMagic) {
                if(pos.PositionType() == POSITION_TYPE_BUY) totalBuyPositions++;
                else if(pos.PositionType() == POSITION_TYPE_SELL) totalSellPositions++;
            }
        }
    }

    // Count pending orders
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(ord.SelectByIndex(i)) {
            if(ord.Symbol() == _Symbol && ord.Magic() == inpMagic) {
                if(ord.OrderType() == ORDER_TYPE_BUY_STOP || ord.OrderType() == ORDER_TYPE_BUY_LIMIT)
                    totalBuyPositions++;
                else if(ord.OrderType() == ORDER_TYPE_SELL_STOP || ord.OrderType() == ORDER_TYPE_SELL_LIMIT)
                    totalSellPositions++;
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check for trading signals                                        |
//+------------------------------------------------------------------+
void CheckTradingSignals() {
    // Look for bullish signals
    if(totalBuyPositions < MaxPositions) {
        double buyEntry = FindBuyEntry();
        if(buyEntry > 0 && IsBuySignalValid(buyEntry)) {
            ExecuteBuyOrder(buyEntry);
        }
    }

    // Look for bearish signals
    if(totalSellPositions < MaxPositions) {
        double sellEntry = FindSellEntry();
        if(sellEntry > 0 && IsSellSignalValid(sellEntry)) {
            ExecuteSellOrder(sellEntry);
        }
    }
}

//+------------------------------------------------------------------+
//| Find buy entry point (swing high breakout)                      |
//+------------------------------------------------------------------+
double FindBuyEntry() {
    for(int i = SwingBars; i < LookbackBars; i++) {
        double high = iHigh(_Symbol, Timeframe, i);

        // Check if this is a swing high
        if(IsSwingHigh(i)) {
            // Check breakout strength
            double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            double breakoutDistance = currentPrice - high;

            if(breakoutDistance >= MinBreakoutStrength * currentATR) {
                // Check if order distance is appropriate
                double orderDistance = OrderDistanceATR * currentATR;
                if(currentPrice <= high + orderDistance) {
                    return high + (orderDistance * 0.5); // Place order slightly above the high
                }
            }
        }
    }
    return -1;
}

//+------------------------------------------------------------------+
//| Find sell entry point (swing low breakout)                      |
//+------------------------------------------------------------------+
double FindSellEntry() {
    for(int i = SwingBars; i < LookbackBars; i++) {
        double low = iLow(_Symbol, Timeframe, i);

        // Check if this is a swing low
        if(IsSwingLow(i)) {
            // Check breakout strength
            double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            double breakoutDistance = low - currentPrice;

            if(breakoutDistance >= MinBreakoutStrength * currentATR) {
                // Check if order distance is appropriate
                double orderDistance = OrderDistanceATR * currentATR;
                if(currentPrice >= low - orderDistance) {
                    return low - (orderDistance * 0.5); // Place order slightly below the low
                }
            }
        }
    }
    return -1;
}

//+------------------------------------------------------------------+
//| Check if bar is a swing high                                     |
//+------------------------------------------------------------------+
bool IsSwingHigh(int barIndex) {
    double centerHigh = iHigh(_Symbol, Timeframe, barIndex);

    // Check bars to the left and right
    for(int i = 1; i <= SwingBars; i++) {
        if(iHigh(_Symbol, Timeframe, barIndex - i) >= centerHigh) return false;
        if(iHigh(_Symbol, Timeframe, barIndex + i) >= centerHigh) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| Check if bar is a swing low                                      |
//+------------------------------------------------------------------+
bool IsSwingLow(int barIndex) {
    double centerLow = iLow(_Symbol, Timeframe, barIndex);

    // Check bars to the left and right
    for(int i = 1; i <= SwingBars; i++) {
        if(iLow(_Symbol, Timeframe, barIndex - i) <= centerLow) return false;
        if(iLow(_Symbol, Timeframe, barIndex + i) <= centerLow) return false;
    }
    return true;
}

//+------------------------------------------------------------------+
//| Validate buy signal with multiple confirmations                  |
//+------------------------------------------------------------------+
bool IsBuySignalValid(double entryPrice) {
    int confirmations = 0;
    int requiredConfirmations = 0;

    // RSI confirmation
    if(UseRSIFilter) {
        requiredConfirmations++;
        double rsi[];
        if(CopyBuffer(handleRSI, 0, 1, 1, rsi) > 0) {
            if(rsi[0] < RSI_Overbought && rsi[0] > RSI_Oversold) {
                confirmations++;
            }
        }
    }

    // MACD confirmation
    if(UseMACDFilter) {
        requiredConfirmations++;
        double macdMain[], macdSignal[];
        if(CopyBuffer(handleMACD, 0, 1, 2, macdMain) > 0 &&
           CopyBuffer(handleMACD, 1, 1, 2, macdSignal) > 0) {
            if(macdMain[0] > macdSignal[0] && macdMain[1] <= macdSignal[1]) {
                confirmations++; // MACD bullish crossover
            }
        }
    }

    // Bollinger Bands confirmation
    if(UseBollingerFilter) {
        requiredConfirmations++;
        double bbUpper[], bbLower[];
        if(CopyBuffer(handleBB, 1, 1, 1, bbUpper) > 0 &&
           CopyBuffer(handleBB, 2, 1, 1, bbLower) > 0) {
            double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
            if(currentPrice > bbLower[0] && currentPrice < bbUpper[0]) {
                confirmations++; // Price within BB range
            }
        }
    }

    // Stochastic confirmation
    if(UseStochasticFilter) {
        requiredConfirmations++;
        double stochMain[], stochSignal[];
        if(CopyBuffer(handleStoch, 0, 1, 1, stochMain) > 0 &&
           CopyBuffer(handleStoch, 1, 1, 1, stochSignal) > 0) {
            if(stochMain[0] < 80 && stochMain[0] > 20) {
                confirmations++; // Stochastic not in extreme zones
            }
        }
    }

    // Trend confirmation
    if(UseTrendFilter) {
        requiredConfirmations++;
        if(IsBullishTrend()) {
            confirmations++;
        }
    }

    // Require at least 70% of confirmations
    double confirmationRatio = requiredConfirmations > 0 ? (double)confirmations / requiredConfirmations : 1.0;
    return (confirmationRatio >= 0.7);
}

//+------------------------------------------------------------------+
//| Validate sell signal with multiple confirmations                 |
//+------------------------------------------------------------------+
bool IsSellSignalValid(double entryPrice) {
    int confirmations = 0;
    int requiredConfirmations = 0;

    // RSI confirmation
    if(UseRSIFilter) {
        requiredConfirmations++;
        double rsi[];
        if(CopyBuffer(handleRSI, 0, 1, 1, rsi) > 0) {
            if(rsi[0] > RSI_Oversold && rsi[0] < RSI_Overbought) {
                confirmations++;
            }
        }
    }

    // MACD confirmation
    if(UseMACDFilter) {
        requiredConfirmations++;
        double macdMain[], macdSignal[];
        if(CopyBuffer(handleMACD, 0, 1, 2, macdMain) > 0 &&
           CopyBuffer(handleMACD, 1, 1, 2, macdSignal) > 0) {
            if(macdMain[0] < macdSignal[0] && macdMain[1] >= macdSignal[1]) {
                confirmations++; // MACD bearish crossover
            }
        }
    }

    // Bollinger Bands confirmation
    if(UseBollingerFilter) {
        requiredConfirmations++;
        double bbUpper[], bbLower[];
        if(CopyBuffer(handleBB, 1, 1, 1, bbUpper) > 0 &&
           CopyBuffer(handleBB, 2, 1, 1, bbLower) > 0) {
            double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
            if(currentPrice > bbLower[0] && currentPrice < bbUpper[0]) {
                confirmations++; // Price within BB range
            }
        }
    }

    // Stochastic confirmation
    if(UseStochasticFilter) {
        requiredConfirmations++;
        double stochMain[], stochSignal[];
        if(CopyBuffer(handleStoch, 0, 1, 1, stochMain) > 0 &&
           CopyBuffer(handleStoch, 1, 1, 1, stochSignal) > 0) {
            if(stochMain[0] < 80 && stochMain[0] > 20) {
                confirmations++; // Stochastic not in extreme zones
            }
        }
    }

    // Trend confirmation
    if(UseTrendFilter) {
        requiredConfirmations++;
        if(IsBearishTrend()) {
            confirmations++;
        }
    }

    // Require at least 70% of confirmations
    double confirmationRatio = requiredConfirmations > 0 ? (double)confirmations / requiredConfirmations : 1.0;
    return (confirmationRatio >= 0.7);
}

//+------------------------------------------------------------------+
//| Check if trend is bullish                                        |
//+------------------------------------------------------------------+
bool IsBullishTrend() {
    double emaFast[], emaSlow[], htfMA[];

    if(CopyBuffer(handleEMA_Fast, 0, 1, 1, emaFast) > 0 &&
       CopyBuffer(handleEMA_Slow, 0, 1, 1, emaSlow) > 0 &&
       CopyBuffer(handleMA_HTF, 0, 1, 1, htfMA) > 0) {

        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        // Local trend: Fast EMA > Slow EMA
        // Higher timeframe trend: Price > HTF MA
        return (emaFast[0] > emaSlow[0] && currentPrice > htfMA[0]);
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check if trend is bearish                                        |
//+------------------------------------------------------------------+
bool IsBearishTrend() {
    double emaFast[], emaSlow[], htfMA[];

    if(CopyBuffer(handleEMA_Fast, 0, 1, 1, emaFast) > 0 &&
       CopyBuffer(handleEMA_Slow, 0, 1, 1, emaSlow) > 0 &&
       CopyBuffer(handleMA_HTF, 0, 1, 1, htfMA) > 0) {

        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        // Local trend: Fast EMA < Slow EMA
        // Higher timeframe trend: Price < HTF MA
        return (emaFast[0] < emaSlow[0] && currentPrice < htfMA[0]);
    }
    return false;
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder(double entryPrice) {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Calculate TP and SL
    double tp, sl;
    if(UseDynamicTPSL) {
        tp = entryPrice + (ATR_TP_Multiplier * currentATR);
        sl = entryPrice - (ATR_SL_Multiplier * currentATR);
    } else {
        tp = entryPrice + (TpPoints * _Point);
        sl = entryPrice - (Slpoints * _Point);
    }

    // Check minimum risk-reward ratio
    double riskReward = (tp - entryPrice) / (entryPrice - sl);
    if(riskReward < MinRiskReward) return;

    // Calculate position size
    double lots = CalculatePositionSize(entryPrice - sl);
    if(lots <= 0) return;

    // Set expiration
    datetime expiration = TimeCurrent() + (ExpirationBars * PeriodSeconds(Timeframe));

    // Place order
    if(ask <= entryPrice) {
        // Market order if price is at or below entry
        trade.Buy(lots, _Symbol, 0, sl, tp, TradeComment);
    } else {
        // Pending order if price is above entry
        trade.BuyStop(lots, entryPrice, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, expiration, TradeComment);
    }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder(double entryPrice) {
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Calculate TP and SL
    double tp, sl;
    if(UseDynamicTPSL) {
        tp = entryPrice - (ATR_TP_Multiplier * currentATR);
        sl = entryPrice + (ATR_SL_Multiplier * currentATR);
    } else {
        tp = entryPrice - (TpPoints * _Point);
        sl = entryPrice + (Slpoints * _Point);
    }

    // Check minimum risk-reward ratio
    double riskReward = (entryPrice - tp) / (sl - entryPrice);
    if(riskReward < MinRiskReward) return;

    // Calculate position size
    double lots = CalculatePositionSize(sl - entryPrice);
    if(lots <= 0) return;

    // Set expiration
    datetime expiration = TimeCurrent() + (ExpirationBars * PeriodSeconds(Timeframe));

    // Place order
    if(bid >= entryPrice) {
        // Market order if price is at or above entry
        trade.Sell(lots, _Symbol, 0, sl, tp, TradeComment);
    } else {
        // Pending order if price is below entry
        trade.SellStop(lots, entryPrice, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, expiration, TradeComment);
    }
}

//+------------------------------------------------------------------+
//| Calculate position size based on risk                            |
//+------------------------------------------------------------------+
double CalculatePositionSize(double slDistance) {
    if(slDistance <= 0) return 0;

    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);

    // Use equity if it's lower than balance (account in drawdown)
    double accountValue = MathMin(balance, equity);

    // Calculate risk amount
    double riskAmount = accountValue * RiskPercent / 100.0;

    // Limit maximum risk per trade
    double maxRiskAmount = accountValue * MaxRiskPerTrade / 100.0;
    riskAmount = MathMin(riskAmount, maxRiskAmount);

    // Get symbol properties
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double volumeLimit = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_LIMIT);

    // Calculate money per lot step
    double moneyPerLotStep = slDistance / tickSize * tickValue * lotStep;
    if(moneyPerLotStep <= 0) return 0;

    // Calculate lot size
    double lots = MathFloor(riskAmount / moneyPerLotStep) * lotStep;

    // Apply limits
    if(volumeLimit > 0) lots = MathMin(lots, volumeLimit);
    lots = MathMin(lots, maxLot);
    lots = MathMax(lots, minLot);

    return NormalizeDouble(lots, 2);
}

//+------------------------------------------------------------------+
//| Enhanced trailing stop function                                  |
//+------------------------------------------------------------------+
void TrailStop() {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Magic() == inpMagic && pos.Symbol() == _Symbol) {
                ulong ticket = pos.Ticket();
                double currentSL = pos.StopLoss();
                double currentTP = pos.TakeProfit();
                double openPrice = pos.PriceOpen();
                double newSL = 0;

                if(pos.PositionType() == POSITION_TYPE_BUY) {
                    double profit = bid - openPrice;
                    double triggerDistance = UseDynamicTPSL ? TslTriggerATR * currentATR : TslTriggerPoints * _Point;
                    double trailDistance = UseDynamicTPSL ? TslATR * currentATR : TslPoints * _Point;

                    if(profit > triggerDistance) {
                        newSL = bid - trailDistance;

                        // Only move SL if it's better than current
                        if(newSL > currentSL + _Point) {
                            trade.PositionModify(ticket, newSL, currentTP);
                        }
                    }
                }
                else if(pos.PositionType() == POSITION_TYPE_SELL) {
                    double profit = openPrice - ask;
                    double triggerDistance = UseDynamicTPSL ? TslTriggerATR * currentATR : TslTriggerPoints * _Point;
                    double trailDistance = UseDynamicTPSL ? TslATR * currentATR : TslPoints * _Point;

                    if(profit > triggerDistance) {
                        newSL = ask + trailDistance;

                        // Only move SL if it's better than current
                        if(newSL < currentSL - _Point || currentSL == 0) {
                            trade.PositionModify(ticket, newSL, currentTP);
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Close all pending orders                                         |
//+------------------------------------------------------------------+
void CloseAllOrders() {
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        if(ord.SelectByIndex(i)) {
            if(ord.Symbol() == _Symbol && ord.Magic() == inpMagic) {
                trade.OrderDelete(ord.Ticket());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Close all positions (emergency function)                         |
//+------------------------------------------------------------------+
void CloseAllPositions() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == inpMagic) {
                trade.PositionClose(pos.Ticket());
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Print trading statistics                                          |
//+------------------------------------------------------------------+
void PrintStatistics() {
    static datetime lastPrint = 0;
    datetime currentTime = TimeCurrent();

    // Print statistics every hour
    if(currentTime - lastPrint >= 3600) {
        lastPrint = currentTime;

        Print("=== Enhanced Scalping Robot Statistics ===");
        Print("Current ATR: ", DoubleToString(currentATR, _Digits));
        Print("Buy Positions: ", totalBuyPositions);
        Print("Sell Positions: ", totalSellPositions);
        Print("Account Equity: ", DoubleToString(accountEquity, 2));
        Print("Max Drawdown: ", DoubleToString(maxDrawdown, 2), "%");

        // Calculate spread
        double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
        double spreadPips = spread / (10 * _Point);
        Print("Current Spread: ", DoubleToString(spreadPips, 1), " pips");

        Print("Trading Allowed: ", IsTradingAllowed() ? "YES" : "NO");
        Print("============================================");
    }
}
