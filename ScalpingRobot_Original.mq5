//+------------------------------------------------------------------+
//| ScalpingRobot.mq5                                               |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, MetaQuotes Ltd."
#property link      "https://www.mql5.com"
#property version   "1.00"

#include <Trade/Trade.mqh>
CTrade         trade;
CPositionInfo  pos;
COrderInfo     ord;

input group "*** Trading Inputs*"
input double RiskPercent = 2;            // Risk as a % of trading capital
input int TpPoints = 200;                // Take Profit (10points = 1pip)
input int Slpoints = 200;                // Stop Loss Points
input int TslTriggerPoints = 15;         // Points in profit before trailing SL
input int TslPoints = 10;                // Trailing SL points
input ENUM_TIMEFRAMES Timeframe = PERIOD_CURRENT;
input int inpMagic = 298347;             // EA Magic Number
input string TradeComment = "Scalping Robot";

// --- ENUMS MUST BE CAST CORRECTLY ---
enum StartHour {
    SH_Inactive = 0,
    SH_0100 = 1, SH_0200 = 2, SH_0300 = 3, SH_0400 = 4, SH_0500 = 5,
    SH_0600 = 6, SH_0700 = 7, SH_0800 = 8, SH_0900 = 9, SH_1000 = 10,
    SH_1100 = 11, SH_1200 = 12, SH_1300 = 13, SH_1400 = 14, SH_1500 = 15,
    SH_1600 = 16, SH_1700 = 17, SH_1800 = 18, SH_1900 = 19, SH_2000 = 20,
    SH_2100 = 21, SH_2200 = 22, SH_2300 = 23
};

enum EndHour {
    EH_Inactive = 0,
    EH_0100 = 1, EH_0200 = 2, EH_0300 = 3, EH_0400 = 4, EH_0500 = 5,
    EH_0600 = 6, EH_0700 = 7, EH_0800 = 8, EH_0900 = 9, EH_1000 = 10,
    EH_1100 = 11, EH_1200 = 12, EH_1300 = 13, EH_1400 = 14, EH_1500 = 15,
    EH_1600 = 16, EH_1700 = 17, EH_1800 = 18, EH_1900 = 19, EH_2000 = 20,
    EH_2100 = 21, EH_2200 = 22, EH_2300 = 23
};

input StartHour SHInput = SH_0700;
input EndHour EHInput = EH_2100;

int SHChoice;
int EHChoice;
int BarsN = 5;
int ExpirationBars = 100;
int OrderDistPoints = 100;

//+------------------------------------------------------------------+
int OnInit() {
    trade.SetExpertMagicNumber(inpMagic);
    return(INIT_SUCCEEDED);
}
//+------------------------------------------------------------------+
void OnDeinit(const int reason) { }
//+------------------------------------------------------------------+
void OnTick() {

    TrailStop();
    if(!IsNewBar()) return;

    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);

    int Hournow = time.hour;
    SHChoice = (int)SHInput;
    EHChoice = (int)EHInput;

    if(Hournow < SHChoice) { CloseAllOrders(); return; }
    if(Hournow >= EHChoice && EHChoice != 0) { CloseAllOrders(); return; }

    int BuyTotal = 0, SellTotal = 0;

    for (int i = PositionsTotal() - 1; i >= 0; i--) {
        pos.SelectByIndex(i);
        if(pos.PositionType() == POSITION_TYPE_BUY && pos.Symbol() == _Symbol && pos.Magic() == inpMagic) BuyTotal++;
        if(pos.PositionType() == POSITION_TYPE_SELL && pos.Symbol() == _Symbol && pos.Magic() == inpMagic) SellTotal++;
    }

    for (int i = OrdersTotal() - 1; i >= 0; i--) {
        ord.SelectByIndex(i);
        if(ord.OrderType() == ORDER_TYPE_BUY_STOP && ord.Symbol() == _Symbol && ord.Magic() == inpMagic) BuyTotal++;
        if(ord.OrderType() == ORDER_TYPE_SELL_STOP && ord.Symbol() == _Symbol && ord.Magic() == inpMagic) SellTotal++;
    }

    if(BuyTotal <= 0) {
        double high = findHigh();
        if(high > 0) SendBuyOrder(high);
    }

    if(SellTotal <= 0) {
        double low = findLow();
        if(low > 0) SendSellOrder(low);
    }
}

//+------------------------------------------------------------------+
double findHigh() {
    for(int i = 0; i < 200; i++) {
        double high = iHigh(_Symbol, Timeframe, i);
        if(i > BarsN && iHighest(_Symbol, Timeframe, MODE_HIGH, BarsN * 2 + 1, i - BarsN) == i)
            return high;
    }
    return -1;
}

double findLow() {
    for(int i = 0; i < 200; i++) {
        double low = iLow(_Symbol, Timeframe, i);
        if(i > BarsN && iLowest(_Symbol, Timeframe, MODE_LOW, BarsN * 2 + 1, i - BarsN) == i)
            return low;
    }
    return -1;
}

bool IsNewBar() {
    static datetime previousTime = 0;
    datetime currentTime = iTime(_Symbol, Timeframe, 0);
    if(previousTime != currentTime) {
        previousTime = currentTime;
        return true;
    }
    return false;
}

void SendBuyOrder(double entry) {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    if(ask > entry - OrderDistPoints * _Point) return;

    double tp = entry + TpPoints * _Point;
    double sl = entry - Slpoints * _Point;

    double lots = RiskPercent > 0 ? calcLots(entry - sl) : 0.01;

    datetime expiration = iTime(_Symbol, Timeframe, 0) + ExpirationBars * PeriodSeconds(Timeframe);
    trade.BuyStop(lots, entry, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, expiration);
}

void SendSellOrder(double entry) {
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    if(bid < entry + OrderDistPoints * _Point) return;

    double tp = entry - TpPoints * _Point;
    double sl = entry + Slpoints * _Point;

    double lots = RiskPercent > 0 ? calcLots(sl - entry) : 0.01;

    datetime expiration = iTime(_Symbol, Timeframe, 0) + ExpirationBars * PeriodSeconds(Timeframe);
    trade.SellStop(lots, entry, _Symbol, sl, tp, ORDER_TIME_SPECIFIED, expiration);
}

double calcLots(double slPoints) {
    double risk = AccountInfoDouble(ACCOUNT_BALANCE) * RiskPercent / 100;

    double ticksize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double tickvalue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotstep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double minvolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxvolume = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    double volumelimit = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_LIMIT);

    double moneyPerLotstep = slPoints / ticksize * tickvalue * lotstep;
    double lots = MathFloor(risk / moneyPerLotstep) * lotstep;

    lots = MathMin(lots, volumelimit > 0 ? volumelimit : lots);
    lots = MathMin(lots, maxvolume);
    lots = MathMax(lots, minvolume);

    return NormalizeDouble(lots, 2);
}

void CloseAllOrders() {
    for(int i = OrdersTotal() - 1; i >= 0; i--) {
        ord.SelectByIndex(i);
        if(ord.Symbol() == _Symbol && ord.Magic() == inpMagic)
            trade.OrderDelete(ord.Ticket());
    }
}

void TrailStop() {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Magic() == inpMagic && pos.Symbol() == _Symbol) {
                ulong ticket = pos.Ticket();
                double sl = 0, tp = pos.TakeProfit();

                if(pos.PositionType() == POSITION_TYPE_BUY) {
                    if(bid - pos.PriceOpen() > TslTriggerPoints * _Point) {
                        sl = bid - (TslPoints * _Point);
                        if(sl > pos.StopLoss())
                            trade.PositionModify(ticket, sl, tp);
                    }
                } else if(pos.PositionType() == POSITION_TYPE_SELL) {
                    if(pos.PriceOpen() - ask > TslTriggerPoints * _Point) {
                        sl = ask + (TslPoints * _Point);
                        if(sl < pos.StopLoss())
                            trade.PositionModify(ticket, sl, tp);
                    }
                }
            }
        }
    }
}
