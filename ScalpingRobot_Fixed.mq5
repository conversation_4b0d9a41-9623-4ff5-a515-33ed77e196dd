//+------------------------------------------------------------------+
//| ScalpingRobot_Fixed.mq5                                         |
//| Ultra-Conservative Capital Preservation Strategy - DEBUGGED     |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Conservative Trading Systems"
#property link      "https://www.mql5.com"
#property version   "4.01"
#property description "Ultra-Conservative EA - FULLY DEBUGGED VERSION"

#include <Trade/Trade.mqh>
#include <Trade/DealInfo.mqh>

CTrade         trade;
CPositionInfo  pos;
COrderInfo     ord;
CDealInfo      deal;

// Indicator handles
int handleATR, handleRSI, handleMACD, handleEMA_Fast, handleEMA_Slow;

//+------------------------------------------------------------------+
//| ULTRA-CONSERVATIVE RISK MANAGEMENT                              |
//+------------------------------------------------------------------+
input group "=== EMERGENCY RISK CONTROLS ==="
input double MaxRiskPerTrade = 0.25;               // MAXIMUM 0.25% risk per trade
input double EmergencyDailyLoss = 5.0;             // Emergency stop at 5% daily loss
input int MaxConsecutiveLosses = 2;                // Stop after 2 consecutive losses
input double BreakevenTriggerPips = 10;            // Move to breakeven after 10 pips profit
input double MaxTradeHours = 4;                    // Maximum 4 hours per trade

input group "=== CONSERVATIVE POSITION SIZING ==="
input bool UseATRBasedSizing = true;               // Reduce size during high volatility
input double MaxVolatilityMultiplier = 0.5;        // Reduce size by 50% in high volatility
input double MinPositionSize = 0.01;               // Minimum lot size
input double MaxPositionSize = 0.1;                // Maximum lot size cap

input group "=== MULTI-CONFIRMATION SYSTEM ==="
input int RequiredConfirmations = 3;               // Require 3 out of 4 confirmations
input bool UseRSIConfirmation = true;              // RSI confirmation
input bool UseMACDConfirmation = true;             // MACD confirmation
input bool UseTrendConfirmation = true;            // EMA trend confirmation
input bool UsePriceActionConfirmation = true;      // Price action confirmation

input group "=== STRICT TREND FOLLOWING ==="
input int EMA_Fast_Period = 21;                    // Fast EMA for trend
input int EMA_Slow_Period = 50;                    // Slow EMA for trend
input double TrendStrengthMinimum = 20;            // Minimum pips between EMAs for trend

input group "=== TECHNICAL INDICATORS ==="
input int RSI_Period = 14;                         // RSI period
input double RSI_BuyThreshold = 45;                // RSI must be above this for buy
input double RSI_SellThreshold = 55;               // RSI must be below this for sell
input int MACD_Fast = 12;                          // MACD fast EMA
input int MACD_Slow = 26;                          // MACD slow EMA
input int MACD_Signal = 9;                         // MACD signal line
input int ATR_Period = 14;                         // ATR period

input group "=== PROFIT PROTECTION ==="
input double MinRiskReward = 3.0;                  // MINIMUM 1:3 risk-reward
input double PartialTPRatio = 1.5;                 // Take 50% profit at 1:1.5
input double TrailingStartPips = 20;               // Start trailing after 20 pips profit
input double TrailingStepATR = 1.0;                // Trailing step in ATR units

input group "=== TIME FILTERS ==="
input int TradingStartHour = 6;                    // Start trading at 06:00 GMT
input int TradingEndHour = 22;                     // Stop trading at 22:00 GMT
input bool AvoidNewsHours = true;                  // Avoid major news hours

input group "=== PERFORMANCE MONITORING ==="
input bool ShowPerformanceInfo = true;             // Show performance in comments
input int PerformanceReviewTrades = 10;            // Review performance over last N trades
input double MinWinRateThreshold = 40.0;           // Pause if win rate below 40%

input int inpMagic = 298347;                       // EA Magic Number
input string TradeComment = "Conservative Scalper";

input group "=== DEBUG SETTINGS ==="
input bool EnableDebugMode = true;                 // Enable detailed logging
input bool EmergencyMode = false;                  // Emergency stop all trading

//+------------------------------------------------------------------+
//| GLOBAL VARIABLES FOR RISK MANAGEMENT                            |
//+------------------------------------------------------------------+
datetime lastBarTime = 0;
datetime dailyResetTime = 0;
double dailyStartBalance = 0;
double currentATR = 0;

// Performance tracking
int totalTrades = 0;
int winningTrades = 0;
int consecutiveLosses = 0;
double currentDrawdown = 0;
bool tradingPaused = false;
string pauseReason = "";

// Trade history for performance analysis
double tradeResults[10];  // Last 10 trade results
int tradeHistoryIndex = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    trade.SetExpertMagicNumber(inpMagic);
    
    // Initialize indicators
    if(!InitializeIndicators()) {
        Print("CRITICAL ERROR: Failed to initialize indicators!");
        return INIT_FAILED;
    }
    
    // Initialize risk management
    dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    dailyResetTime = TimeCurrent();
    
    // Reset performance tracking
    totalTrades = 0;
    winningTrades = 0;
    consecutiveLosses = 0;
    tradingPaused = false;
    
    // Initialize trade history array
    ArrayInitialize(tradeResults, 0);
    
    Print("=== ULTRA-CONSERVATIVE SCALPING ROBOT INITIALIZED ===");
    Print("Symbol: ", _Symbol);
    Print("Max Risk Per Trade: ", MaxRiskPerTrade, "%");
    Print("Emergency Daily Loss Limit: ", EmergencyDailyLoss, "%");
    Print("Required Confirmations: ", RequiredConfirmations, " out of 4");
    Print("Minimum Risk:Reward: 1:", MinRiskReward);
    Print("CAPITAL PRESERVATION MODE ACTIVE");
    Print("=====================================================");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Initialize all indicators                                        |
//+------------------------------------------------------------------+
bool InitializeIndicators() {
    // ATR for volatility and position sizing
    handleATR = iATR(_Symbol, _Period, ATR_Period);
    if(handleATR == INVALID_HANDLE) {
        Print("ERROR: Failed to create ATR indicator");
        return false;
    }
    
    // RSI for momentum confirmation
    handleRSI = iRSI(_Symbol, _Period, RSI_Period, PRICE_CLOSE);
    if(handleRSI == INVALID_HANDLE) {
        Print("ERROR: Failed to create RSI indicator");
        return false;
    }
    
    // MACD for trend momentum
    handleMACD = iMACD(_Symbol, _Period, MACD_Fast, MACD_Slow, MACD_Signal, PRICE_CLOSE);
    if(handleMACD == INVALID_HANDLE) {
        Print("ERROR: Failed to create MACD indicator");
        return false;
    }
    
    // EMAs for trend direction
    handleEMA_Fast = iMA(_Symbol, _Period, EMA_Fast_Period, 0, MODE_EMA, PRICE_CLOSE);
    handleEMA_Slow = iMA(_Symbol, _Period, EMA_Slow_Period, 0, MODE_EMA, PRICE_CLOSE);
    
    if(handleEMA_Fast == INVALID_HANDLE || handleEMA_Slow == INVALID_HANDLE) {
        Print("ERROR: Failed to create EMA indicators");
        return false;
    }
    
    Print("SUCCESS: All indicators initialized");
    return true;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    // Release indicator handles
    if(handleATR != INVALID_HANDLE) IndicatorRelease(handleATR);
    if(handleRSI != INVALID_HANDLE) IndicatorRelease(handleRSI);
    if(handleMACD != INVALID_HANDLE) IndicatorRelease(handleMACD);
    if(handleEMA_Fast != INVALID_HANDLE) IndicatorRelease(handleEMA_Fast);
    if(handleEMA_Slow != INVALID_HANDLE) IndicatorRelease(handleEMA_Slow);
    
    Print("Ultra-Conservative Scalping Robot deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function - ULTRA CONSERVATIVE APPROACH              |
//+------------------------------------------------------------------+
void OnTick() {
    // EMERGENCY STOP CHECK
    if(EmergencyMode) {
        if(EnableDebugMode) Print("EMERGENCY MODE ACTIVE - ALL TRADING STOPPED");
        return;
    }
    
    // Update ATR on every tick for risk calculations
    UpdateATR();
    
    // Manage existing positions on every tick
    ManageActivePositions();
    
    // Check for new bar before looking for new trades
    if(!IsNewBar()) return;
    
    // CRITICAL RISK CHECKS - STOP TRADING IF ANY FAIL
    if(!PerformCriticalRiskChecks()) {
        if(EnableDebugMode) Print("CRITICAL RISK CHECK FAILED - TRADING PAUSED");
        return;
    }
    
    // Update daily performance tracking
    UpdateDailyTracking();
    
    // Display performance information
    if(ShowPerformanceInfo) {
        DisplayPerformanceInfo();
    }
    
    // Only look for new trades if we have no positions (ultra-conservative)
    int totalPositions = CountAllPositions();
    if(totalPositions > 0) {
        if(EnableDebugMode) Print("EXISTING POSITION ACTIVE - NO NEW TRADES");
        return;
    }
    
    // Check if trading is allowed based on time and conditions
    if(!IsTradeTimeAllowed()) {
        if(EnableDebugMode) Print("OUTSIDE TRADING HOURS");
        return;
    }
    
    // Look for ultra-high-quality trading opportunities
    CheckForTradingOpportunities();
}

//+------------------------------------------------------------------+
//| Update ATR value for risk calculations                           |
//+------------------------------------------------------------------+
void UpdateATR() {
    double atr[];
    ArraySetAsSeries(atr, true);
    if(CopyBuffer(handleATR, 0, 0, 1, atr) > 0) {
        currentATR = atr[0];
    }
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar() {
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    if(lastBarTime != currentBarTime) {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Perform critical risk checks before any trading                  |
//+------------------------------------------------------------------+
bool PerformCriticalRiskChecks() {
    // Check if trading is manually paused
    if(tradingPaused) {
        if(EnableDebugMode) Print("TRADING PAUSED: ", pauseReason);
        return false;
    }

    // Check consecutive losses limit
    if(consecutiveLosses >= MaxConsecutiveLosses) {
        tradingPaused = true;
        pauseReason = "Maximum consecutive losses reached";
        Print("TRADING PAUSED: ", pauseReason);
        return false;
    }

    // Check daily loss limit
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(dailyStartBalance > 0) {
        double dailyLoss = (dailyStartBalance - currentBalance) / dailyStartBalance * 100;

        if(dailyLoss >= EmergencyDailyLoss) {
            tradingPaused = true;
            pauseReason = "Emergency daily loss limit reached: " + DoubleToString(dailyLoss, 2) + "%";
            Print("EMERGENCY STOP: ", pauseReason);
            return false;
        }
    }

    // Check win rate if we have enough trades
    if(totalTrades >= PerformanceReviewTrades) {
        double winRate = (double)winningTrades / totalTrades * 100;
        if(winRate < MinWinRateThreshold) {
            tradingPaused = true;
            pauseReason = "Win rate below threshold: " + DoubleToString(winRate, 1) + "%";
            Print("TRADING PAUSED: ", pauseReason);
            return false;
        }
    }

    // Check maximum drawdown
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    if(balance > 0) {
        currentDrawdown = (balance - equity) / balance * 100;

        if(currentDrawdown > 15.0) { // Hard stop at 15% drawdown
            tradingPaused = true;
            pauseReason = "Maximum drawdown exceeded: " + DoubleToString(currentDrawdown, 2) + "%";
            Print("TRADING PAUSED: ", pauseReason);
            return false;
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Count all positions for this EA                                  |
//+------------------------------------------------------------------+
int CountAllPositions() {
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == inpMagic) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Check if trading time is allowed                                 |
//+------------------------------------------------------------------+
bool IsTradeTimeAllowed() {
    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);
    int currentHour = time.hour;

    // Avoid low liquidity hours (22:00-06:00 GMT)
    if(currentHour >= TradingEndHour || currentHour < TradingStartHour) {
        return false;
    }

    // Avoid major news hours if enabled
    if(AvoidNewsHours) {
        // Avoid 8:30, 10:00, 14:00, 16:00 GMT (major news times)
        int minute = time.hour * 60 + time.min;
        int newsMinutes[] = {510, 600, 840, 960}; // News times in minutes

        for(int i = 0; i < ArraySize(newsMinutes); i++) {
            if(MathAbs(minute - newsMinutes[i]) <= 30) { // 30 minutes before/after
                return false;
            }
        }
    }

    return true;
}

//+------------------------------------------------------------------+
//| Update daily tracking and reset if new day                       |
//+------------------------------------------------------------------+
void UpdateDailyTracking() {
    MqlDateTime currentTime, resetTime;
    TimeToStruct(TimeCurrent(), currentTime);
    TimeToStruct(dailyResetTime, resetTime);

    // Reset daily tracking at start of new day
    if(currentTime.day != resetTime.day) {
        dailyStartBalance = AccountInfoDouble(ACCOUNT_BALANCE);
        dailyResetTime = TimeCurrent();

        // Reset consecutive losses if new day (give fresh start)
        if(consecutiveLosses > 0) {
            Print("NEW DAY - RESETTING CONSECUTIVE LOSSES FROM ", consecutiveLosses, " TO 0");
            consecutiveLosses = 0;
            if(tradingPaused && StringFind(pauseReason, "consecutive") >= 0) {
                tradingPaused = false;
                pauseReason = "";
                Print("TRADING RESUMED - NEW DAY");
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Display performance information                                   |
//+------------------------------------------------------------------+
void DisplayPerformanceInfo() {
    double winRate = totalTrades > 0 ? (double)winningTrades / totalTrades * 100 : 0;
    double currentBalance = AccountInfoDouble(ACCOUNT_BALANCE);
    double dailyPL = currentBalance - dailyStartBalance;
    double dailyPLPercent = dailyStartBalance > 0 ? dailyPL / dailyStartBalance * 100 : 0;

    string info = StringFormat(
        "Conservative Scalper | Trades: %d | Win Rate: %.1f%% | Daily P/L: %.2f%% | Consecutive Losses: %d | Drawdown: %.2f%%",
        totalTrades, winRate, dailyPLPercent, consecutiveLosses, currentDrawdown
    );

    Comment(info);

    if(EnableDebugMode) {
        Print("=== PERFORMANCE UPDATE ===");
        Print("Total Trades: ", totalTrades);
        Print("Win Rate: ", DoubleToString(winRate, 1), "%");
        Print("Daily P/L: ", DoubleToString(dailyPLPercent, 2), "%");
        Print("Consecutive Losses: ", consecutiveLosses);
        Print("Current Drawdown: ", DoubleToString(currentDrawdown, 2), "%");
        Print("Trading Status: ", tradingPaused ? "PAUSED" : "ACTIVE");
        if(tradingPaused) Print("Pause Reason: ", pauseReason);
    }
}

//+------------------------------------------------------------------+
//| Manage active positions - breakeven, trailing, time exits       |
//+------------------------------------------------------------------+
void ManageActivePositions() {
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == inpMagic) {
                ulong ticket = pos.Ticket();
                double openPrice = pos.PriceOpen();
                double currentSL = pos.StopLoss();
                double currentTP = pos.TakeProfit();
                datetime openTime = pos.Time();

                // Check for time-based exit (max 4 hours)
                if(TimeCurrent() - openTime > MaxTradeHours * 3600) {
                    Print("CLOSING POSITION DUE TO TIME LIMIT: ", ticket);
                    trade.PositionClose(ticket);
                    continue;
                }

                if(pos.PositionType() == POSITION_TYPE_BUY) {
                    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
                    double profitPips = (currentPrice - openPrice) / _Point;

                    // Move to breakeven after 10 pips profit
                    if(profitPips >= BreakevenTriggerPips && currentSL < openPrice) {
                        Print("MOVING BUY POSITION TO BREAKEVEN: ", ticket);
                        trade.PositionModify(ticket, openPrice + 5 * _Point, currentTP);
                    }

                    // Partial profit taking at 1:1.5 ratio
                    if(profitPips >= BreakevenTriggerPips * PartialTPRatio) {
                        // Close 50% of position (simplified - close entire position for now)
                        Print("TAKING PARTIAL PROFIT ON BUY: ", ticket);
                        trade.PositionClose(ticket);
                    }

                    // Trailing stop after 20 pips profit
                    if(profitPips >= TrailingStartPips && currentATR > 0) {
                        double newSL = currentPrice - (TrailingStepATR * currentATR);
                        if(newSL > currentSL + _Point) {
                            trade.PositionModify(ticket, newSL, currentTP);
                        }
                    }
                }
                else if(pos.PositionType() == POSITION_TYPE_SELL) {
                    double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
                    double profitPips = (openPrice - currentPrice) / _Point;

                    // Move to breakeven after 10 pips profit
                    if(profitPips >= BreakevenTriggerPips && (currentSL > openPrice || currentSL == 0)) {
                        Print("MOVING SELL POSITION TO BREAKEVEN: ", ticket);
                        trade.PositionModify(ticket, openPrice - 5 * _Point, currentTP);
                    }

                    // Partial profit taking at 1:1.5 ratio
                    if(profitPips >= BreakevenTriggerPips * PartialTPRatio) {
                        Print("TAKING PARTIAL PROFIT ON SELL: ", ticket);
                        trade.PositionClose(ticket);
                    }

                    // Trailing stop after 20 pips profit
                    if(profitPips >= TrailingStartPips && currentATR > 0) {
                        double newSL = currentPrice + (TrailingStepATR * currentATR);
                        if(newSL < currentSL - _Point || currentSL == 0) {
                            trade.PositionModify(ticket, newSL, currentTP);
                        }
                    }
                }
            }
        }
    }
}

//+------------------------------------------------------------------+
//| Check for ultra-high-quality trading opportunities               |
//+------------------------------------------------------------------+
void CheckForTradingOpportunities() {
    if(EnableDebugMode) Print("=== CHECKING FOR TRADING OPPORTUNITIES ===");

    // Get all indicator values with proper array handling
    double rsi[], macdMain[], macdSignal[], emaFast[], emaSlow[];
    ArraySetAsSeries(rsi, true);
    ArraySetAsSeries(macdMain, true);
    ArraySetAsSeries(macdSignal, true);
    ArraySetAsSeries(emaFast, true);
    ArraySetAsSeries(emaSlow, true);

    if(CopyBuffer(handleRSI, 0, 0, 2, rsi) <= 0 ||
       CopyBuffer(handleMACD, 0, 0, 2, macdMain) <= 0 ||
       CopyBuffer(handleMACD, 1, 0, 2, macdSignal) <= 0 ||
       CopyBuffer(handleEMA_Fast, 0, 0, 2, emaFast) <= 0 ||
       CopyBuffer(handleEMA_Slow, 0, 0, 2, emaSlow) <= 0) {
        if(EnableDebugMode) Print("ERROR: Failed to get indicator values");
        return;
    }

    // Check for BUY signal with multi-confirmation
    int buyConfirmations = 0;
    string buyReasons = "";

    // 1. RSI Confirmation for BUY
    if(UseRSIConfirmation && rsi[0] > RSI_BuyThreshold && rsi[0] < 80) {
        buyConfirmations++;
        buyReasons += "RSI_OK ";
    }

    // 2. MACD Confirmation for BUY
    if(UseMACDConfirmation && macdMain[0] > macdSignal[0] && macdMain[1] <= macdSignal[1]) {
        buyConfirmations++;
        buyReasons += "MACD_BULLISH_CROSS ";
    }

    // 3. Trend Confirmation for BUY
    if(UseTrendConfirmation) {
        double trendStrength = (emaFast[0] - emaSlow[0]) / _Point;
        if(emaFast[0] > emaSlow[0] && trendStrength >= TrendStrengthMinimum) {
            buyConfirmations++;
            buyReasons += "UPTREND_STRONG ";
        }
    }

    // 4. Price Action Confirmation for BUY
    if(UsePriceActionConfirmation) {
        double high1 = iHigh(_Symbol, _Period, 1);
        double high2 = iHigh(_Symbol, _Period, 2);
        double close1 = iClose(_Symbol, _Period, 1);
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

        if(close1 > high2 && currentPrice > high1) {
            buyConfirmations++;
            buyReasons += "BREAKOUT_BUY ";
        }
    }

    // Check for SELL signal with multi-confirmation
    int sellConfirmations = 0;
    string sellReasons = "";

    // 1. RSI Confirmation for SELL
    if(UseRSIConfirmation && rsi[0] < RSI_SellThreshold && rsi[0] > 20) {
        sellConfirmations++;
        sellReasons += "RSI_OK ";
    }

    // 2. MACD Confirmation for SELL
    if(UseMACDConfirmation && macdMain[0] < macdSignal[0] && macdMain[1] >= macdSignal[1]) {
        sellConfirmations++;
        sellReasons += "MACD_BEARISH_CROSS ";
    }

    // 3. Trend Confirmation for SELL
    if(UseTrendConfirmation) {
        double trendStrength = (emaSlow[0] - emaFast[0]) / _Point;
        if(emaFast[0] < emaSlow[0] && trendStrength >= TrendStrengthMinimum) {
            sellConfirmations++;
            sellReasons += "DOWNTREND_STRONG ";
        }
    }

    // 4. Price Action Confirmation for SELL
    if(UsePriceActionConfirmation) {
        double low1 = iLow(_Symbol, _Period, 1);
        double low2 = iLow(_Symbol, _Period, 2);
        double close1 = iClose(_Symbol, _Period, 1);
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);

        if(close1 < low2 && currentPrice < low1) {
            sellConfirmations++;
            sellReasons += "BREAKOUT_SELL ";
        }
    }

    if(EnableDebugMode) {
        Print("BUY Confirmations: ", buyConfirmations, "/4 - ", buyReasons);
        Print("SELL Confirmations: ", sellConfirmations, "/4 - ", sellReasons);
        Print("Required Confirmations: ", RequiredConfirmations);
    }

    // Execute trades only if we have enough confirmations
    if(buyConfirmations >= RequiredConfirmations) {
        Print("ULTRA-HIGH-QUALITY BUY SIGNAL DETECTED: ", buyConfirmations, "/4 confirmations");
        Print("Reasons: ", buyReasons);
        ExecuteConservativeBuyOrder();
    }
    else if(sellConfirmations >= RequiredConfirmations) {
        Print("ULTRA-HIGH-QUALITY SELL SIGNAL DETECTED: ", sellConfirmations, "/4 confirmations");
        Print("Reasons: ", sellReasons);
        ExecuteConservativeSellOrder();
    }
    else {
        if(EnableDebugMode) Print("INSUFFICIENT CONFIRMATIONS - NO TRADE");
    }
}

//+------------------------------------------------------------------+
//| Execute ultra-conservative buy order                             |
//+------------------------------------------------------------------+
void ExecuteConservativeBuyOrder() {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);

    // Ensure we have valid ATR
    if(currentATR <= 0) {
        Print("TRADE REJECTED: Invalid ATR value");
        return;
    }

    // Calculate ultra-conservative stop loss (ATR-based)
    double slDistance = currentATR * 1.5; // 1.5 ATR stop loss
    double sl = ask - slDistance;

    // Calculate take profit with minimum 1:3 risk-reward
    double tpDistance = slDistance * MinRiskReward;
    double tp = ask + tpDistance;

    // Verify minimum risk-reward ratio
    double actualRR = tpDistance / slDistance;
    if(actualRR < MinRiskReward) {
        Print("TRADE REJECTED: Risk-reward ratio too low: ", DoubleToString(actualRR, 2));
        return;
    }

    // Calculate ultra-conservative position size
    double lots = CalculateConservativeLotSize(slDistance);
    if(lots < MinPositionSize) {
        Print("TRADE REJECTED: Position size too small: ", DoubleToString(lots, 2));
        return;
    }

    Print("=== EXECUTING ULTRA-CONSERVATIVE BUY ORDER ===");
    Print("Entry Price: ", DoubleToString(ask, _Digits));
    Print("Stop Loss: ", DoubleToString(sl, _Digits), " (", DoubleToString(slDistance/_Point, 1), " pips)");
    Print("Take Profit: ", DoubleToString(tp, _Digits), " (", DoubleToString(tpDistance/_Point, 1), " pips)");
    Print("Risk:Reward: 1:", DoubleToString(actualRR, 2));
    Print("Position Size: ", DoubleToString(lots, 2), " lots");
    Print("Risk Amount: $", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE) * MaxRiskPerTrade / 100, 2));

    bool result = trade.Buy(lots, _Symbol, ask, sl, tp, TradeComment);

    if(result) {
        totalTrades++;
        Print("ULTRA-CONSERVATIVE BUY ORDER PLACED SUCCESSFULLY!");
        Print("Trade #", totalTrades, " - CAPITAL PRESERVATION MODE");
    } else {
        Print("BUY ORDER FAILED! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute ultra-conservative sell order                            |
//+------------------------------------------------------------------+
void ExecuteConservativeSellOrder() {
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);

    // Ensure we have valid ATR
    if(currentATR <= 0) {
        Print("TRADE REJECTED: Invalid ATR value");
        return;
    }

    // Calculate ultra-conservative stop loss (ATR-based)
    double slDistance = currentATR * 1.5; // 1.5 ATR stop loss
    double sl = bid + slDistance;

    // Calculate take profit with minimum 1:3 risk-reward
    double tpDistance = slDistance * MinRiskReward;
    double tp = bid - tpDistance;

    // Verify minimum risk-reward ratio
    double actualRR = tpDistance / slDistance;
    if(actualRR < MinRiskReward) {
        Print("TRADE REJECTED: Risk-reward ratio too low: ", DoubleToString(actualRR, 2));
        return;
    }

    // Calculate ultra-conservative position size
    double lots = CalculateConservativeLotSize(slDistance);
    if(lots < MinPositionSize) {
        Print("TRADE REJECTED: Position size too small: ", DoubleToString(lots, 2));
        return;
    }

    Print("=== EXECUTING ULTRA-CONSERVATIVE SELL ORDER ===");
    Print("Entry Price: ", DoubleToString(bid, _Digits));
    Print("Stop Loss: ", DoubleToString(sl, _Digits), " (", DoubleToString(slDistance/_Point, 1), " pips)");
    Print("Take Profit: ", DoubleToString(tp, _Digits), " (", DoubleToString(tpDistance/_Point, 1), " pips)");
    Print("Risk:Reward: 1:", DoubleToString(actualRR, 2));
    Print("Position Size: ", DoubleToString(lots, 2), " lots");
    Print("Risk Amount: $", DoubleToString(AccountInfoDouble(ACCOUNT_BALANCE) * MaxRiskPerTrade / 100, 2));

    bool result = trade.Sell(lots, _Symbol, bid, sl, tp, TradeComment);

    if(result) {
        totalTrades++;
        Print("ULTRA-CONSERVATIVE SELL ORDER PLACED SUCCESSFULLY!");
        Print("Trade #", totalTrades, " - CAPITAL PRESERVATION MODE");
    } else {
        Print("SELL ORDER FAILED! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate ultra-conservative lot size                            |
//+------------------------------------------------------------------+
double CalculateConservativeLotSize(double slDistance) {
    // Use equity if lower than balance (account in drawdown)
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double equity = AccountInfoDouble(ACCOUNT_EQUITY);
    double accountValue = MathMin(balance, equity);

    if(accountValue <= 0) {
        Print("ERROR: Invalid account value");
        return MinPositionSize;
    }

    // Ultra-conservative risk: maximum 0.25% per trade
    double riskAmount = accountValue * MaxRiskPerTrade / 100.0;

    // Reduce position size during high volatility
    if(UseATRBasedSizing && currentATR > 0) {
        // Get average ATR for comparison
        double atrArray[];
        ArraySetAsSeries(atrArray, true);
        if(CopyBuffer(handleATR, 0, 0, 20, atrArray) > 0) {
            double avgATR = 0;
            for(int i = 0; i < 20; i++) avgATR += atrArray[i];
            avgATR /= 20;

            if(currentATR > avgATR * 1.5) { // High volatility
                riskAmount *= MaxVolatilityMultiplier; // Reduce by 50%
                if(EnableDebugMode) Print("HIGH VOLATILITY DETECTED - REDUCING POSITION SIZE BY 50%");
            }
        }
    }

    // Calculate lot size based on risk
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);

    if(tickSize <= 0 || tickValue <= 0 || lotStep <= 0) {
        Print("ERROR: Invalid symbol properties");
        return MinPositionSize;
    }

    double moneyPerLotStep = slDistance / tickSize * tickValue * lotStep;
    if(moneyPerLotStep <= 0) return MinPositionSize;

    double lots = MathFloor(riskAmount / moneyPerLotStep) * lotStep;

    // Apply strict limits
    lots = MathMax(lots, MinPositionSize);
    lots = MathMin(lots, MaxPositionSize);

    // Additional safety: never risk more than calculated
    double actualRisk = lots * slDistance / tickSize * tickValue;
    double maxAllowedRisk = accountValue * MaxRiskPerTrade / 100.0;

    if(actualRisk > maxAllowedRisk) {
        lots = MathFloor(maxAllowedRisk / (slDistance / tickSize * tickValue) / lotStep) * lotStep;
        lots = MathMax(lots, MinPositionSize);
    }

    if(EnableDebugMode) {
        Print("=== POSITION SIZING CALCULATION ===");
        Print("Account Value: $", DoubleToString(accountValue, 2));
        Print("Risk Amount: $", DoubleToString(riskAmount, 2), " (", MaxRiskPerTrade, "%)");
        Print("SL Distance: ", DoubleToString(slDistance/_Point, 1), " pips");
        Print("Calculated Lots: ", DoubleToString(lots, 2));
        Print("Actual Risk: $", DoubleToString(actualRisk, 2));
    }

    return NormalizeDouble(lots, 2);
}

//+------------------------------------------------------------------+
//| Handle trade results for performance tracking                    |
//+------------------------------------------------------------------+
void OnTradeTransaction(const MqlTradeTransaction& trans,
                        const MqlTradeRequest& request,
                        const MqlTradeResult& result) {
    // Track closed positions for performance analysis
    if(trans.type == TRADE_TRANSACTION_DEAL_ADD) {
        if(trans.symbol == _Symbol) {
            if(deal.SelectByIndex(trans.deal)) {
                if(deal.Magic() == inpMagic && deal.Entry() == DEAL_ENTRY_OUT) {
                    double profit = deal.Profit();

                    // Update performance tracking
                    if(profit > 0) {
                        winningTrades++;
                        consecutiveLosses = 0; // Reset consecutive losses
                    } else if(profit < 0) {
                        consecutiveLosses++;
                    }

                    // Add to trade history
                    tradeResults[tradeHistoryIndex] = profit;
                    tradeHistoryIndex = (tradeHistoryIndex + 1) % 10;

                    Print("=== TRADE CLOSED ===");
                    Print("Profit: $", DoubleToString(profit, 2));
                    Print("Consecutive Losses: ", consecutiveLosses);
                    Print("Win Rate: ", totalTrades > 0 ? DoubleToString((double)winningTrades/totalTrades*100, 1) : "0", "%");

                    // Check if we need to pause trading due to consecutive losses
                    if(consecutiveLosses >= MaxConsecutiveLosses) {
                        tradingPaused = true;
                        pauseReason = "Maximum consecutive losses reached: " + IntegerToString(consecutiveLosses);
                        Print("TRADING PAUSED: ", pauseReason);
                    }
                }
            }
        }
    }
}
