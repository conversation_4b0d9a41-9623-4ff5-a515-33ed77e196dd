//+------------------------------------------------------------------+
//| ScalpingRobot_Enhanced_Debug.mq5                                |
//| Enhanced version with advanced risk management and signals      |
//+------------------------------------------------------------------+
#property copyright "Copyright 2025, Enhanced Trading Systems"
#property link      "https://www.mql5.com"
#property version   "3.00"
#property description "Enhanced EA with superior risk management and signal quality"

#include <Trade/Trade.mqh>

CTrade         trade;
CPositionInfo  pos;
COrderInfo     ord;

// Indicator handles
int handleATR, handleRSI, handleMACD, handleEMA_Fast, handleEMA_Slow;

// Enhanced inputs for professional trading
input group "=== RISK MANAGEMENT ==="
input double RiskPercent = 0.5;                    // Risk per trade (% of balance) - REDUCED
input double MaxDailyLoss = 3.0;                   // Maximum daily loss (% of balance)
input double MaxDrawdown = 10.0;                   // Maximum total drawdown (%)
input int MaxConsecutiveLosses = 3;                // Max consecutive losses before pause
input bool UseEquityBasedSizing = true;            // Use equity instead of balance for sizing

input group "=== DYNAMIC STOP LOSS ==="
input bool UseATRStopLoss = true;                  // Use ATR-based stop loss
input double ATR_SL_Multiplier = 1.2;              // ATR multiplier for stop loss
input int ATR_Period = 14;                         // ATR calculation period
input int FixedSLPoints = 80;                      // Fixed SL if not using ATR (REDUCED)
input bool UseTrailingStop = true;                 // Enable trailing stop
input double TrailATRMultiplier = 0.8;             // ATR multiplier for trailing

input group "=== PROFIT MANAGEMENT ==="
input double MinRiskReward = 2.0;                  // Minimum risk:reward ratio
input bool UsePartialTP = true;                    // Take partial profits
input double PartialTPPercent = 50.0;              // % of position to close at first TP
input double BreakevenATRMultiplier = 1.0;         // Move SL to breakeven after this ATR profit
input int MaxPositionHours = 8;                    // Max hours to hold position

input group "=== SIGNAL FILTERS ==="
input bool UseRSIFilter = true;                    // Use RSI filter
input int RSI_Period = 14;                         // RSI period
input double RSI_Oversold = 30;                    // RSI oversold level
input double RSI_Overbought = 70;                  // RSI overbought level

input bool UseMACDFilter = true;                   // Use MACD filter
input int MACD_Fast = 12;                          // MACD fast EMA
input int MACD_Slow = 26;                          // MACD slow EMA
input int MACD_Signal = 9;                         // MACD signal line

input bool UseTrendFilter = true;                  // Use trend filter
input int EMA_Fast_Period = 21;                    // Fast EMA period
input int EMA_Slow_Period = 50;                    // Slow EMA period

input bool UseVolatilityFilter = true;             // Use volatility filter
input double MinATRForTrading = 0.0001;            // Minimum ATR to trade
input double MaxATRForTrading = 0.01;              // Maximum ATR to trade

input int inpMagic = 298347;                       // EA Magic Number
input string TradeComment = "Enhanced Scalping Robot";

input group "=== DEBUG SETTINGS ==="
input bool EnableDebugMode = true;                 // Enable detailed logging
input bool DisableAllFilters = false;              // Disable all filters for testing
input bool ForceTradeSignals = false;              // Force trade signals for testing

input group "=== TIME FILTER ==="
enum StartHour {
    SH_Inactive = 0, SH_0700 = 7, SH_0800 = 8, SH_0900 = 9, SH_1000 = 10
};
enum EndHour {
    EH_Inactive = 0, EH_2100 = 21, EH_2200 = 22, EH_2300 = 23
};

input StartHour SHInput = SH_0700;                // Trading start hour
input EndHour EHInput = EH_2100;                  // Trading end hour

// Global variables
datetime lastBarTime = 0;
int debugCounter = 0;

//+------------------------------------------------------------------+
//| Expert initialization function                                   |
//+------------------------------------------------------------------+
int OnInit() {
    trade.SetExpertMagicNumber(inpMagic);
    
    Print("=== DEBUG SCALPING ROBOT INITIALIZED ===");
    Print("Symbol: ", _Symbol);
    Print("Timeframe: ", EnumToString(_Period));
    Print("Debug Mode: ", EnableDebugMode ? "ENABLED" : "DISABLED");
    Print("Disable Filters: ", DisableAllFilters ? "YES" : "NO");
    Print("Force Signals: ", ForceTradeSignals ? "YES" : "NO");
    Print("========================================");
    
    return INIT_SUCCEEDED;
}

//+------------------------------------------------------------------+
//| Expert deinitialization function                                |
//+------------------------------------------------------------------+
void OnDeinit(const int reason) {
    Print("Debug Scalping Robot deinitialized. Reason: ", reason);
}

//+------------------------------------------------------------------+
//| Expert tick function                                             |
//+------------------------------------------------------------------+
void OnTick() {
    // Check for new bar
    if(!IsNewBar()) return;
    
    debugCounter++;
    
    if(EnableDebugMode) {
        Print("=== TICK #", debugCounter, " ===");
        Print("Time: ", TimeToString(TimeCurrent()));
        Print("Bid: ", DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_BID), _Digits));
        Print("Ask: ", DoubleToString(SymbolInfoDouble(_Symbol, SYMBOL_ASK), _Digits));
    }
    
    // Check trading conditions step by step
    if(!CheckTradingConditions()) return;
    
    // Count current positions
    int buyPositions = CountPositions(POSITION_TYPE_BUY);
    int sellPositions = CountPositions(POSITION_TYPE_SELL);
    
    if(EnableDebugMode) {
        Print("Current Buy Positions: ", buyPositions);
        Print("Current Sell Positions: ", sellPositions);
    }
    
    // Look for trading opportunities
    if(buyPositions == 0) {
        CheckBuySignal();
    }
    
    if(sellPositions == 0) {
        CheckSellSignal();
    }
    
    if(EnableDebugMode) {
        Print("=== END TICK #", debugCounter, " ===");
    }
}

//+------------------------------------------------------------------+
//| Check if new bar formed                                          |
//+------------------------------------------------------------------+
bool IsNewBar() {
    datetime currentBarTime = iTime(_Symbol, _Period, 0);
    if(lastBarTime != currentBarTime) {
        lastBarTime = currentBarTime;
        return true;
    }
    return false;
}

//+------------------------------------------------------------------+
//| Check all trading conditions                                     |
//+------------------------------------------------------------------+
bool CheckTradingConditions() {
    // Time filter
    if(!DisableAllFilters && !IsWithinTradingHours()) {
        if(EnableDebugMode) Print("BLOCKED: Outside trading hours");
        return false;
    }
    
    // Spread filter
    if(!DisableAllFilters && !IsSpreadAcceptable()) {
        if(EnableDebugMode) Print("BLOCKED: Spread too high");
        return false;
    }
    
    // Market state
    if(!IsMarketOpen()) {
        if(EnableDebugMode) Print("BLOCKED: Market closed");
        return false;
    }
    
    if(EnableDebugMode) Print("PASSED: All trading conditions OK");
    return true;
}

//+------------------------------------------------------------------+
//| Check if within trading hours                                    |
//+------------------------------------------------------------------+
bool IsWithinTradingHours() {
    MqlDateTime time;
    TimeToStruct(TimeCurrent(), time);
    int currentHour = time.hour;
    
    int startHour = (int)SHInput;
    int endHour = (int)EHInput;
    
    if(startHour == 0 && endHour == 0) return true; // No time restriction
    
    bool withinHours;
    if(startHour <= endHour) {
        withinHours = (currentHour >= startHour && currentHour < endHour);
    } else {
        withinHours = (currentHour >= startHour || currentHour < endHour);
    }
    
    if(EnableDebugMode) {
        Print("Current Hour: ", currentHour, ", Start: ", startHour, ", End: ", endHour);
        Print("Within Hours: ", withinHours ? "YES" : "NO");
    }
    
    return withinHours;
}

//+------------------------------------------------------------------+
//| Check if spread is acceptable                                    |
//+------------------------------------------------------------------+
bool IsSpreadAcceptable() {
    double spread = SymbolInfoInteger(_Symbol, SYMBOL_SPREAD) * _Point;
    double spreadPips = spread / (10 * _Point);
    bool acceptable = (spreadPips <= 5.0); // Max 5 pips
    
    if(EnableDebugMode) {
        Print("Current Spread: ", DoubleToString(spreadPips, 1), " pips");
        Print("Spread Acceptable: ", acceptable ? "YES" : "NO");
    }
    
    return acceptable;
}

//+------------------------------------------------------------------+
//| Check if market is open                                          |
//+------------------------------------------------------------------+
bool IsMarketOpen() {
    return (SymbolInfoInteger(_Symbol, SYMBOL_TRADE_MODE) == SYMBOL_TRADE_MODE_FULL);
}

//+------------------------------------------------------------------+
//| Count positions by type                                          |
//+------------------------------------------------------------------+
int CountPositions(ENUM_POSITION_TYPE posType) {
    int count = 0;
    for(int i = PositionsTotal() - 1; i >= 0; i--) {
        if(pos.SelectByIndex(i)) {
            if(pos.Symbol() == _Symbol && pos.Magic() == inpMagic && pos.PositionType() == posType) {
                count++;
            }
        }
    }
    return count;
}

//+------------------------------------------------------------------+
//| Check for buy signal                                             |
//+------------------------------------------------------------------+
void CheckBuySignal() {
    if(EnableDebugMode) Print("Checking BUY signal...");
    
    bool signalFound = false;
    
    if(ForceTradeSignals) {
        signalFound = true;
        if(EnableDebugMode) Print("FORCED BUY SIGNAL");
    } else {
        // Simple breakout logic
        double high1 = iHigh(_Symbol, _Period, 1);
        double high2 = iHigh(_Symbol, _Period, 2);
        double high3 = iHigh(_Symbol, _Period, 3);
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
        
        if(high1 > high2 && high1 > high3 && currentPrice > high1) {
            signalFound = true;
            if(EnableDebugMode) Print("BUY SIGNAL: Breakout detected");
        }
    }
    
    if(signalFound) {
        ExecuteBuyOrder();
    } else {
        if(EnableDebugMode) Print("NO BUY SIGNAL");
    }
}

//+------------------------------------------------------------------+
//| Check for sell signal                                            |
//+------------------------------------------------------------------+
void CheckSellSignal() {
    if(EnableDebugMode) Print("Checking SELL signal...");
    
    bool signalFound = false;
    
    if(ForceTradeSignals) {
        signalFound = true;
        if(EnableDebugMode) Print("FORCED SELL SIGNAL");
    } else {
        // Simple breakout logic
        double low1 = iLow(_Symbol, _Period, 1);
        double low2 = iLow(_Symbol, _Period, 2);
        double low3 = iLow(_Symbol, _Period, 3);
        double currentPrice = SymbolInfoDouble(_Symbol, SYMBOL_BID);
        
        if(low1 < low2 && low1 < low3 && currentPrice < low1) {
            signalFound = true;
            if(EnableDebugMode) Print("SELL SIGNAL: Breakout detected");
        }
    }
    
    if(signalFound) {
        ExecuteSellOrder();
    } else {
        if(EnableDebugMode) Print("NO SELL SIGNAL");
    }
}

//+------------------------------------------------------------------+
//| Execute buy order                                                |
//+------------------------------------------------------------------+
void ExecuteBuyOrder() {
    double ask = SymbolInfoDouble(_Symbol, SYMBOL_ASK);
    double sl = ask - (Slpoints * _Point);
    double tp = ask + (TpPoints * _Point);
    
    double lots = CalculateLotSize(Slpoints * _Point);
    
    if(EnableDebugMode) {
        Print("=== EXECUTING BUY ORDER ===");
        Print("Price: ", DoubleToString(ask, _Digits));
        Print("SL: ", DoubleToString(sl, _Digits));
        Print("TP: ", DoubleToString(tp, _Digits));
        Print("Lots: ", DoubleToString(lots, 2));
    }
    
    bool result = trade.Buy(lots, _Symbol, ask, sl, tp, TradeComment);
    
    if(result) {
        Print("BUY ORDER PLACED SUCCESSFULLY!");
    } else {
        Print("BUY ORDER FAILED! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Execute sell order                                               |
//+------------------------------------------------------------------+
void ExecuteSellOrder() {
    double bid = SymbolInfoDouble(_Symbol, SYMBOL_BID);
    double sl = bid + (Slpoints * _Point);
    double tp = bid - (TpPoints * _Point);
    
    double lots = CalculateLotSize(Slpoints * _Point);
    
    if(EnableDebugMode) {
        Print("=== EXECUTING SELL ORDER ===");
        Print("Price: ", DoubleToString(bid, _Digits));
        Print("SL: ", DoubleToString(sl, _Digits));
        Print("TP: ", DoubleToString(tp, _Digits));
        Print("Lots: ", DoubleToString(lots, 2));
    }
    
    bool result = trade.Sell(lots, _Symbol, bid, sl, tp, TradeComment);
    
    if(result) {
        Print("SELL ORDER PLACED SUCCESSFULLY!");
    } else {
        Print("SELL ORDER FAILED! Error: ", trade.ResultRetcode(), " - ", trade.ResultRetcodeDescription());
    }
}

//+------------------------------------------------------------------+
//| Calculate lot size                                               |
//+------------------------------------------------------------------+
double CalculateLotSize(double slDistance) {
    double balance = AccountInfoDouble(ACCOUNT_BALANCE);
    double riskAmount = balance * RiskPercent / 100.0;
    
    double tickSize = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_SIZE);
    double tickValue = SymbolInfoDouble(_Symbol, SYMBOL_TRADE_TICK_VALUE);
    double lotStep = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_STEP);
    double minLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MIN);
    double maxLot = SymbolInfoDouble(_Symbol, SYMBOL_VOLUME_MAX);
    
    double moneyPerLotStep = slDistance / tickSize * tickValue * lotStep;
    if(moneyPerLotStep <= 0) return minLot;
    
    double lots = MathFloor(riskAmount / moneyPerLotStep) * lotStep;
    lots = MathMax(lots, minLot);
    lots = MathMin(lots, maxLot);
    
    if(EnableDebugMode) {
        Print("Risk Amount: ", DoubleToString(riskAmount, 2));
        Print("Calculated Lots: ", DoubleToString(lots, 2));
    }
    
    return NormalizeDouble(lots, 2);
}
